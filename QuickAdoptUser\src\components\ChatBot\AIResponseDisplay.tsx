import React from "react";
import { aichaticon } from "../../assets/icons/icons";
import { Box, Typography } from "@mui/material";

// Type for structured feature
interface ParsedBlock {
  title: string;
  steps: string[];
  description?: string;
}

function isListFormat(text: string): boolean {
  const listPatterns = [
    /^\d+\.\s/m,           // Numbered lists (1. 2. 3.)
    /^[-•*]\s/m,           // Bullet points (- • *)
    /^\*\*.*:\*\*/m,       // Bold headers with colons
    /###/,                 // Section headers
  ];

  return listPatterns.some(pattern => pattern.test(text));
}

// Main component
export default function AIResponseDisplay({ text }: { text: string }) {
  const parsedBlocks = parseAIResponse(text);
  const isPlainParagraph = parsedBlocks.length === 0;
  const shouldUseListStyles = isListFormat(text);

  return (
    <Box>

      <Typography sx={{
        fontSize: "14px",
        color: " #fff",
        background: "#323578",
        padding: "12px 14px",
        borderRadius: "10px",
        maxWidth: "95%",
      }}> {isPlainParagraph ? (
  <div>{text}</div>
) : (
  parsedBlocks.map((block, idx) => (
    <div key={idx} >
      <div style={{color:"#fff",fontSize:"14px", fontFamily:"Gotham Pro"}}>{block.title}</div>
      {shouldUseListStyles && block.steps.length > 0 && (
        <ol style={{fontFamily:"Gotham Pro"}} className="">
          {block.steps.map((step, i) => (
            <li key={i}>{step}</li>
          ))}
        </ol>

      )}
      {block.description && (
        <p style={{fontFamily:"Gotham Pro"}} className="">{block.description}</p>
      )}
    </div>
  ))
          )}
          </Typography>
</Box>

  );
}



// Helper function to parse structured responses
function parseAIResponse(input: string): ParsedBlock[] {
  const sections = input.split(/###/).map(s => s.trim()).filter(Boolean);
  const parsed: ParsedBlock[] = [];

  for (const section of sections.length > 0 ? sections : [input]) {
    const lines = section.split('\n').map(l => l.trim()).filter(Boolean);
    if (lines.length === 0) continue;

    let title = lines[0].replace(/^Example Feature:\s*/, '');
    let steps: string[] = [];
    let description = '';
    let extraDescriptionLines: string[] = [];

    for (const line of lines.slice(1)) {
      if (/description/i.test(line)) {
        description = line.replace(/-?\s*\*\*Description\*\*:\s*/i, '');
      } else if (/^\d+\.\s|^\-\s|^•\s/.test(line)) {
        steps.push(line.replace(/^\d+\.\s|^\-\s|^•\s/, '').trim());
      } else if (/^\*\*/.test(line)) {
        steps.push(line.replace(/\*\*/g, '').replace(':', '').trim());
      } else {
        extraDescriptionLines.push(line);
      }
    }

    if (!description && extraDescriptionLines.length > 0) {
      description = extraDescriptionLines.join(' ');
    }

    parsed.push({ title, steps, description });
  }

  return parsed;
}


