import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  IconButton,
  TextField,
  Paper,
  InputAdornment,
  Slide,
  Tooltip,
  CircularProgress,
  Divider,
  Button,
  LinearProgress
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import * as signalR from '@microsoft/signalr';
import {
  isSpeechRecognitionSupported,
  startSpeechRecognition,
  stopSpeechRecognition
} from '../../services/SpeechRecognitionService';
import {
  speak,
  stop as stopSpeaking,
  setCurrentMessageId,
  isSpeechSynthesisSupported
} from '../../services/TextToSpeechService';
import {
  ChatModalStyle,
  ChatHeaderStyle,
  ChatBodyStyle,
  ChatFooterStyle,
  MessageContainerStyle,
  UserMessageStyle,
  BotMessageStyle,
  MicButtonStyle,
  ListeningIndicatorStyle,
  SpeechButtonStyle
} from './ChatBot.style';
import { SendMessageSSE, AssistantResponse as ServiceAssistantResponse,FetchWelcomeAudio,streamWelcomeAudio,speakWithOpenAITTS, fetchTTSStream, IsOpenAIKeyEnabledForAccount } from '../Services/AssistantService';
import AIResponseDisplay from './AIResponseDisplay';
import { aichaticon, usrchaticon } from '../../assets/icons/icons';
import robot from "../../assets/icons/robot.png";
import { Preview } from '@mui/icons-material';
import { userApiService,speakModal } from '../Services/APIservice';
import { useSnackbar } from '../../hooks/SnackbarContext';
import workAgentSignalRService, { WorkAgentCallbacks, UserInputRequest } from '../../services/SignalRService';
import { GetAgentById } from '../Services/AgentService';


// Using ServiceAssistantResponse instead of this interface
// interface AssistantResponse {
//   ResponseType: string;
//   Message: string;
// }

interface Message {
  text: string;
  isUser: boolean;
  timestamp: Date;
  id?: string; // Unique identifier for each message
  guide: any;
  isStatusMessage?: boolean; // Flag for status messages
  isInputRequest?: boolean; // Flag for user input requests
  userInputRequest?: UserInputRequest; // User input request data
}
let base64Audio:any="";

interface ChatModalProps {
  open: boolean;
  onClose: () => void;
  guide: any;
  isFromAi: boolean;
  setGuide: (guide: any) => void;
  setIsFromAi: (isFromAi: boolean) => void;
  setIsOpen: (open: any) => void;
  newThreadCreated?: boolean;
  setNewThreadCreated?: React.Dispatch<React.SetStateAction<boolean>>;
  isWelcomeMessageShown: boolean;
  setWelcomeMessageShown: (isWelcomeMessageShown: any) => void;
  messages: any;
  setMessages: (messages: any) => void;
  isReWelcomeMessageShown: boolean;
  setReWelcomeMessageShown: (isReWelcomeMessageShown: any) => void;
  setShowFeedbackPopup: (show: boolean) => void; // Add this prop
  feedbackInfoState: boolean | null; // <-- Add this line
  setFeedbackInfoState: (val: boolean | null) => void; // <-- Add this line
  isOpenAiKeyProvided: any;
  setOpenAiKeyProvided: (isOpenAiKeyProvided: any) => void;
  // Worker agent related props
  onStartTraining?: () => void;
  setStartTraining?: any;
  setBackgroundMode?: any;
  setBindingData?: any;
  setWorkerAgentVisible?: any;
  agentId?: string;
  setAgentId?: any;

}

const ChatModal: React.FC<ChatModalProps> = ({
  open,
  onClose,
  guide,
  isFromAi,
  setGuide,
  setIsFromAi,
  setIsOpen,
  newThreadCreated,
  setNewThreadCreated,
  isWelcomeMessageShown,
  setWelcomeMessageShown,
  messages,
  setMessages,
  isReWelcomeMessageShown,
  setReWelcomeMessageShown,
  setShowFeedbackPopup, // Add to destructure
  feedbackInfoState, // <-- Add this line
  setFeedbackInfoState, // <-- Add this line
  isOpenAiKeyProvided,
  setOpenAiKeyProvided,
  onStartTraining,
  // Worker agent related props

  setStartTraining,
  setBackgroundMode,
  setBindingData,
  setWorkerAgentVisible,
  agentId,
  setAgentId,

}) => {

  // Initialize speakingModal variable and add debug logs
  var speakingModal = speakModal || "ElevenLabs";


  const [inputValue, setInputValue] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [speechRecognitionSupported, setSpeechRecognitionSupported] = useState(false);
  const [speakingMessageId, setSpeakingMessageId] = useState<string | null>(null);
  const [micPermissionError, setMicPermissionError] = useState<string | null>(null);
  const [micPermissionStatus, setMicPermissionStatus] = useState<string>('unknown');
  const [autoSpeakResponse, setAutoSpeakResponse] = useState<boolean>(true);
  const [lastResponseId, setLastResponseId] = useState<string | null>('welcome-message');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [micManuallyDisabled, setMicManuallyDisabled] = useState<boolean>(false);
  const [connectionState, setConnectionState] = useState<signalR.HubConnectionState>(signalR.HubConnectionState.Disconnected);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [firstName, setFirstName] = useState("");
  //const audioRef = useRef<HTMLAudioElement | null>(null);
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);
  const ttsCacheRef = useRef<Record<string, string[]>>({});
  const activeCallbacksRef = useRef<Set<WorkAgentCallbacks>>(new Set());
  const persistentUserInputCallbackRef = useRef<WorkAgentCallbacks | null>(null);
  const { openSnackbar } = useSnackbar();
  let accountId = localStorage.getItem('AccountId') ?? "";

  

  
  const getUserData = () => {
    try {
      const userStatsString = localStorage.getItem('userStats');
      if (userStatsString) {
        return JSON.parse(userStatsString);
      }
      return null;
    } catch (error) {
      return null;
    }
  };

 
  // Utility function to check if we're waiting for user input
  const isWaitingForInput = () => {
    if (messages.length === 0) return false;
    const lastMessage = messages[messages.length - 1];
    return !lastMessage.isUser && (lastMessage.userInputRequest || lastMessage.isInputRequest);
  };

  // Utility function to filter click steps from TrainingFields
  const getClickObjects = (agent: any) => {
    if (!agent || !agent.TrainingFields) return [];
    return agent.TrainingFields.filter((field: any) => {
      const isClick = field.Type === "click" || field.Type === "hover";
      const hasButtonInPath =
        (field.Xpath && !field.Xpath.toLowerCase().includes("button"));
      return isClick && hasButtonInPath;
    });
  };

 
  // const fetchWelcomeAudio = async (text: string) => {
  //   base64Audio = await FetchWelcomeAudio(text);

  //   if (base64Audio) {
  //     const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
  //     audioRef.current = audio;
  //     audio.onplay = () => {
  //       // setSpeakingMessageId('audio-response');
  //       // setLastResponseId('audio-response');
  //     };
  //     audio.onpause = () => {
  //       stopAudio();
  //     };
  //     // Reset speaking state when audio finishes
  //     audio.onended = () => {
  //       setSpeakingMessageId(null);
      
  //       //startSpeech();
  //     };
  //     audio.play();
  //   }
  // };
  const fetchWelcomeAudio = async (text: string) => {
    if(speakingModal === "ElevenLabs")
    {
    const response = await streamWelcomeAudio(text); // Make API call to stream
  
    if (response) {
      const mime = 'audio/mpeg';
      const mediaSource = new MediaSource();
      const audio = new Audio();
      audioRef.current = audio;
      audio.src = URL.createObjectURL(mediaSource);
  
      audio.onplay = () => {
        // Optional: Handle onplay logic
      };
  
      audio.onpause = () => {
        stopAudio();
      };
  
      audio.onended = () => {
        setSpeakingMessageId(null);
      };
  
      mediaSource.addEventListener('sourceopen', () => {
        const sourceBuffer = mediaSource.addSourceBuffer(mime);
        const reader = response.getReader();
  
        const read = async () => {
          const { done, value } = await reader.read();
          if (done) {
            mediaSource.endOfStream();
            return;
          }
          sourceBuffer.appendBuffer(value);
          sourceBuffer.addEventListener('updateend', read, { once: true });
        };
  
        read();
      });
  
      audio.play();
    }
  }
  else{
       base64Audio = await FetchWelcomeAudio(text);

    if (base64Audio) {
      const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
      audioRef.current = audio;
      audio.onplay = () => {
        // setSpeakingMessageId('audio-response');
        // setLastResponseId('audio-response');
      };
      audio.onpause = () => {
        stopAudio();
      };
      // Reset speaking state when audio finishes
      audio.onended = () => {
        setSpeakingMessageId(null);
      
        //startSpeech();
      };
      audio.play();
    }
  }
  };

  // const fetchWelcomeAudio = async (text: string) => {
  //   const base64Chunks = await streamWelcomeAudioChunks(text);
  
  //   if (!base64Chunks || base64Chunks.length === 0) return;
  
  //   const playChunksSequentially = async (chunks: string[]) => {
  //     for (const base64Audio of chunks) {
  //       if (!base64Audio) continue;
  //       const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
  //       await new Promise((resolve) => {
  //         audio.onended = resolve;
  //         audio.onerror = resolve; // continue even if playback fails
  //         audio.play();
  //       });
  //     }
  //     setSpeakingMessageId(null);
  //   };
  
  //   const playAudioChunk = (base64: string): Promise<void> => {
  //     return new Promise((resolve) => {
  //       const audio = new Audio(`data:audio/mpeg;base64,${base64}`);
  //       audioRef.current = audio;
  //       audio.onended = () => resolve();
  //       audio.play(); 
  //     });
  //   };
  
  //   setSpeakingMessageId("welcome-message");
  //   playChunksSequentially(base64Chunks);
  // };
  //working one
  // const playTTSChunks = async (text: string) => {
  //   const res = await fetch("http://localhost:60552/api/Assistant/GetTTSChunks", {
  //     method: "POST",
  //     headers: { "Content-Type": "application/json" },
  //     body: JSON.stringify({ text })
  //   });
  
  //   const chunks = await res.json(); // [{ index, base64 }]
  //   const queue = chunks.map((c: any) => new Blob([Uint8Array.from(atob(c.base64), c => c.charCodeAt(0))], { type: "audio/mpeg" }));
  
  //   const playNext = () => {
  //     if (queue.length === 0) return;
  //     const blob = queue.shift();
  //     const audio = new Audio(URL.createObjectURL(blob));
  //     audio.onended = playNext;
  //     audio.play();
  //   };
  
  //   playNext();
  // };
  const playTTSChunks = async (messageId: string, text: string) => {
    if (speakingMessageId === messageId) {
      stopStreamedAudio();
      setSpeakingMessageId(null);
      return;
    }
  
    stopStreamedAudio(); // stop any existing
    setSpeakingMessageId(messageId);
  
    const queue: string[] = [];
    let isPlaying = false;
    let allChunksReceived = false;
  
    const playNext = () => {
      if (queue.length === 0) {
        if (allChunksReceived) {
          setSpeakingMessageId(null);
          // ✅ Now safe to start mic
          if (shouldAutoRestartMic()) {
            startSpeech();
          }
        }
        return;
      }
  
      const src = queue.shift()!;
      const audio = new Audio(src);
      currentAudioRef.current = audio;
  
      audio.onended = () => {
        URL.revokeObjectURL(src);
        playNext();
      };
  
      audio.onerror = (e) => {
        console.error("Audio playback error", e);
        URL.revokeObjectURL(src);
        playNext();
      };
  
      audio.play().catch((err) => {
        console.error("Playback failed:", err);
        URL.revokeObjectURL(src);
        playNext();
      });
    };
  
    const controller = new AbortController();
    abortControllerRef.current = controller;
  
    await streamTTSChunks(text, controller.signal, (chunk) => {
      if (!chunk.base64) return;
  
      const byteArray = Uint8Array.from(atob(chunk.base64), (c) => c.charCodeAt(0));
      const blob = new Blob([byteArray], { type: "audio/mpeg" });
      const url = URL.createObjectURL(blob);
  
      queue.push(url);
      audioQueueRef.current.push(url);
  
      if (!isPlaying) {
        isPlaying = true;
        playNext();
      }
    });
  
    // ✅ This runs after all chunks are received
    allChunksReceived = true;
  
    // If playback already finished while chunks were being fetched
    if (queue.length === 0 && !isPlaying) {
      setSpeakingMessageId(null);
      if (shouldAutoRestartMic()) {
        startSpeech();
      }
    }
  };
  
  const stopStreamedAudio = () => {
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
    }

    audioQueueRef.current.forEach((url) => {
      try {
        URL.revokeObjectURL(url);
      } catch (err) {
        console.warn("Failed to revoke URL:", err);
      }
    });
    audioQueueRef.current = [];

    if (abortControllerRef.current) {
     // abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    //setSpeakingMessageId(null);
    // if (!isListening && !isProcessing && open) {
    //   startSpeech();
    // }
  };
  

  const streamTTSChunks = async (
    text: string,
    signal: AbortSignal,
    onChunk: (chunk: { index: number; base64: string }) => void
  ) => {
    const stream = await fetchTTSStream(text, signal);
    const reader = stream.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
  
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
  
      buffer += decoder.decode(value, { stream: true });
  
      let boundary = buffer.indexOf("\n\n");
      while (boundary !== -1) {
        const chunkStr = buffer.slice(0, boundary).trim();
        buffer = buffer.slice(boundary + 2);
  
        const dataLine = chunkStr.split("\n").find(line => line.startsWith("data: "));
        if (dataLine) {
          try {
            const jsonStr = dataLine.slice("data: ".length);
            const chunk = JSON.parse(jsonStr);
            onChunk(chunk);
          } catch (err) {
            console.error("JSON parse error:", err);
          }
        }
  
        boundary = buffer.indexOf("\n\n");
      }
    }
  };

  var welcomeText = "";

  var reWelcomeMessageText = "";

  const createWelcomeMessage = () => {
    const userData = getUserData();
  
    if (userData?.Name) {
      setFirstName(userData.Name);
    }
    setWelcomeMessageShown(true);

    const dynamicGreeting = "Hi,This is Dona";
  
    welcomeText = `${dynamicGreeting} `;
    
    setSpeakingMessageId("welcome-message");
    if(speakingModal === "webkit"){
      handleSpeakMessage("welcome-message",welcomeText);
    } else if(speakingModal ==="OpenAi"){
      fetchWelcomeAudio(welcomeText);
    }
    else{
      playTTSChunks("welcome-message",welcomeText);
    }
    
    return {
      text: welcomeText,
      isUser: false,
      timestamp: new Date(),
      id: 'welcome-message',
      guide: null
    };
  }

  const [hasInitialized, setHasInitialized] = useState(false);
  
  const [threadId, setThreadId] = useState<string | null>(localStorage.getItem("ThreadId"));

  useEffect(() => {
    if (open && !isWelcomeMessageShown && welcomeText == "" && messages.length === 0) {
      const welcomeMessageText = createWelcomeMessage();
      setWelcomeMessageShown(true);
      setMessages((prev : any) => [...prev, welcomeMessageText]);
    } else if (open && !isReWelcomeMessageShown && messages.length != 0 && reWelcomeMessageText === "") {
      const reWelcomeMessageText = createReWelcomeMessage();
      setReWelcomeMessageShown(true);
      setMessages((prev:any) => [...prev, reWelcomeMessageText]);
    }
    verifyOpenAIKeyProvided();
  }, [open])
  
  const verifyOpenAIKeyProvided = async () => {
    if ((isOpenAiKeyProvided == "undefined" ||isOpenAiKeyProvided == undefined || isOpenAiKeyProvided == "" || isOpenAiKeyProvided == null) && accountId!="" && accountId!="undefined") {
      
      await IsOpenAIKeyEnabledForAccount(openSnackbar, accountId, setOpenAiKeyProvided);
    }
  }


  const createReWelcomeMessage = () => {
  
    const dynamicGreeting = firstName
      ? `Welcome back, ${firstName}!`
      : `Welcome back `;
  
      reWelcomeMessageText = `${dynamicGreeting}

     `;
    setLastResponseId(`rewelcome-message_${messages.length - 1}`);
      setSpeakingMessageId(`rewelcome-message_${messages.length - 1}`);
      
      if(speakingModal === "webkit"){

        handleSpeakMessage(`rewelcome-message_${messages.length - 1}`,reWelcomeMessageText);

      } else if(speakingModal ==="OpenAi"){
        fetchWelcomeAudio(reWelcomeMessageText);
      }
      else{
        playTTSChunks(`rewelcome-message_${messages.length - 1}`,reWelcomeMessageText);
      }
      
    return {
      text: reWelcomeMessageText,
      isUser: false,
      timestamp: new Date(),
      id: `rewelcome-message_${messages.length - 1}`,
      guide: null
    };
  };


  
  //const [guide,setGuide] = useState();

  // Function to request microphone permission
  const requestMicrophonePermission = async (): Promise<boolean> => {
    try {
      // Request microphone permission explicitly
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // If we get here, permission was granted
      // Stop all tracks to release the microphone
      stream.getTracks().forEach(track => track.stop());

      return true;
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
      return false;
    }
  };

  // Function to check microphone permission status
  const checkMicrophonePermission = async (): Promise<string> => {
    try {
      // Check if the permissions API is supported
      if (navigator.permissions && navigator.permissions.query) {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });

        return permissionStatus.state; // 'granted', 'denied', or 'prompt'
      } else {
        // Fallback for browsers that don't support the permissions API
        return 'unknown';
      }
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return 'unknown';
    }
  };

  // Function to explicitly request microphone permission
  const handleRequestMicrophonePermission = async () => {
    //console.log('Requesting microphone permission...');
    const permissionGranted = await requestMicrophonePermission();

    if (permissionGranted) {
      //console.log('Microphone permission granted');
      setMicPermissionStatus('granted');
      setMicPermissionError(null);
    } else {
      //console.log('Microphone permission denied');
      setMicPermissionStatus('denied');
      setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
    }
  };

  // Check if speech recognition and speech synthesis are supported
  useEffect(() => {
    // Check WebKit speech recognition support
    const recognitionSupported = isSpeechRecognitionSupported();
    setSpeechRecognitionSupported(recognitionSupported);
    if (!recognitionSupported) {
      console.warn('WebKit speech recognition is not supported in this browser');
    }

    // Check if speech synthesis is supported
    const synthesisSupported = isSpeechSynthesisSupported();
    if (!synthesisSupported) {
      console.warn('Speech synthesis is not supported in this browser');
      setAutoSpeakResponse(false); // Disable auto-speak if not supported
    }

    // Check microphone permission status
    const checkPermission = async () => {
      const permissionStatus = await checkMicrophonePermission();
      //console.log('Microphone permission status:', permissionStatus);
      setMicPermissionStatus(permissionStatus);

      // If permission is denied, set the error message
      if (permissionStatus === 'denied') {
        setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
      }
    };

    checkPermission();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Track active callbacks for cleanup

  // Create persistent user input callback that stays active throughout the conversation
  const setupPersistentUserInputCallback = () => {
    if (persistentUserInputCallbackRef.current) {
      console.log('🔄 Persistent user input callback already exists, skipping setup');
      return;
    }

    const persistentCallback: WorkAgentCallbacks = {
      onMessage: () => {}, // Empty - this callback is only for user input requests
      onError: () => {}, // Empty - this callback is only for user input requests
      onUserInputRequest: (inputRequest: UserInputRequest) => {
        console.log('🎯 PERSISTENT: WorkAgent User Input Request:', inputRequest);

        const botMessageId = generateMessageId();

        // Create bot message for the user input request
        const botMessage: Message = {
          text: inputRequest.Message,
          isUser: false,
          timestamp: new Date(),
          id: botMessageId,
          guide: null,
          isStatusMessage: false,
          isInputRequest: true,
          userInputRequest: inputRequest
        };

        setMessages((prev: any) => [...prev, botMessage]);
        setIsProcessing(false); // Allow user to respond

        // Set the last response ID for user input requests
        setLastResponseId(botMessageId);

        // Auto-speak the user input request message
        if (autoSpeakResponse) {
          console.log('🎵 Auto-speaking user input request message:', inputRequest.Message);
          setSpeakingMessageId(botMessageId);

          // Wait a short moment for the UI to update, then speak the request
          setTimeout(() => {
            if (speakModal === "webkit") {
              console.log('🎵 Using webkit TTS for user input request auto-speak');
              handleSpeakMessage(botMessageId, inputRequest.Message);
            } else if (speakModal === "OpenAi") {
              console.log('🎵 Using OpenAI TTS for user input request auto-speak');
              fetchTTSAudio(botMessageId, inputRequest.Message);
            } else {
              console.log('🎵 Using ElevenLabs streaming TTS for user input request auto-speak');
              playTTSChunks(botMessageId, inputRequest.Message);
            }
          }, 300);
        } else {
          // If auto-speak is disabled, start microphone immediately
          if (shouldAutoRestartMic()) {
            console.log('🎤 Starting microphone for user input request (auto-speak disabled)');
            setTimeout(() => {
              startSpeech();
            }, 500); // Small delay to ensure UI updates
          }
        }

        // Keep callbacks active for user response
      }
    };

    persistentUserInputCallbackRef.current = persistentCallback;
    workAgentSignalRService.addCallbacks(persistentCallback);
    console.log('✅ Persistent user input callback set up and registered');
  };

  // Initialize WorkAgent SignalR connection
  useEffect(() => {
    const initializeWorkAgentSignalR = async () => {
      try {
        console.log('ChatModal: Initializing WorkAgent SignalR connection...');
        await workAgentSignalRService.ensureConnection();
        const newConnectionState = workAgentSignalRService.getConnectionState();
        setConnectionState(newConnectionState);
        console.log('ChatModal: WorkAgent SignalR connection established, state:', newConnectionState);

        // Set up persistent user input callback after connection is established
        setupPersistentUserInputCallback();
      } catch (error) {
        console.error('ChatModal: Failed to connect to WorkAgent SignalR:', error);
        setConnectionState(workAgentSignalRService.getConnectionState());
      }
    };

    if (open) {
      // Case 1: Ensure SignalR connection when ChatModal opens
      console.log('ChatModal: Modal opened, ensuring SignalR connection...');
      initializeWorkAgentSignalR();
    } else {
      // Case 2: Clean up ChatModal callbacks and disconnect if no other callbacks exist
      console.log('ChatModal: Modal closed, cleaning up ChatModal callbacks...');
      
      // Clean up all ChatModal callbacks when modal closes
      activeCallbacksRef.current.forEach(callback => {
        console.log('ChatModal: Removing ChatModal callback on modal close');
        workAgentSignalRService.removeCallbacks(callback);
      });
      activeCallbacksRef.current.clear();

      // Clean up persistent user input callback
      if (persistentUserInputCallbackRef.current) {
        console.log('ChatModal: Removing persistent user input callback on modal close');
        workAgentSignalRService.removeCallbacks(persistentUserInputCallbackRef.current);
        persistentUserInputCallbackRef.current = null;
      }
      
      // Check if we should disconnect (only if no other components are using the connection)
      const handleDisconnection = async () => {
        try {
          const remainingCallbacks = workAgentSignalRService.getActiveCallbacksCount();
          console.log(`ChatModal: Remaining active callbacks after cleanup: ${remainingCallbacks}`);
          
          if (remainingCallbacks === 0) {
            console.log('ChatModal: No remaining callbacks, disconnecting SignalR...');
            await workAgentSignalRService.disconnect();
            const newConnectionState = workAgentSignalRService.getConnectionState();
            setConnectionState(newConnectionState);
            console.log('ChatModal: SignalR disconnected successfully, state:', newConnectionState);
          } else {
            console.log('ChatModal: Other components still using SignalR, keeping connection alive');
            setConnectionState(workAgentSignalRService.getConnectionState());
          }
        } catch (error) {
          console.error('ChatModal: Error during disconnection handling:', error);
          setConnectionState(workAgentSignalRService.getConnectionState());
        }
      };
      
      handleDisconnection();
    }

    return () => {
      // Cleanup will be handled when modal closes or component unmounts
    };
  }, [open]);

  // Cleanup when component unmounts completely
  useEffect(() => {
    return () => {
      console.log('ChatModal: Component unmounting, cleaning up...');

      // Clean up all remaining callbacks
      activeCallbacksRef.current.forEach(callback => {
        console.log('ChatModal: Removing callback on component unmount');
        workAgentSignalRService.removeCallbacks(callback);
      });
      activeCallbacksRef.current.clear();

      // Clean up persistent user input callback
      if (persistentUserInputCallbackRef.current) {
        console.log('ChatModal: Removing persistent user input callback on component unmount');
        workAgentSignalRService.removeCallbacks(persistentUserInputCallbackRef.current);
        persistentUserInputCallbackRef.current = null;
      }

      // Stop speech recognition if active
      try {
        stopSpeechRecognition();
      } catch (error) {
        console.log('Speech recognition already stopped or not active');
      }

      // Handle SignalR disconnection on component unmount
      const handleUnmountDisconnection = async () => {
        try {
          const remainingCallbacks = workAgentSignalRService.getActiveCallbacksCount();
          console.log(`ChatModal: Component unmounting, remaining callbacks: ${remainingCallbacks}`);
          
          if (remainingCallbacks === 0) {
            console.log('ChatModal: No remaining callbacks on unmount, disconnecting SignalR...');
            await workAgentSignalRService.disconnect();
            console.log('ChatModal: SignalR disconnected on unmount, final state:', workAgentSignalRService.getConnectionState());
          } else {
            console.log('ChatModal: Other components still using SignalR on unmount, keeping connection alive');
          }
        } catch (error) {
          console.error('ChatModal: Error handling disconnection on unmount:', error);
        }
      };
      
      handleUnmountDisconnection();
      console.log('ChatModal: Cleanup completed');
    };
  }, []); // Empty dependency array means this runs only on unmount

  // Generate a unique ID for messages
  const generateMessageId = (): string => {
    return `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  };

  // Handle speaking a message
  const handleSpeakMessage = (messageId: string, text: string) => {
    if (speakingMessageId === messageId) {
      if(speakModal === "webkit"){
        stopSpeaking();
        if (shouldAutoRestartMic()) {
          setTimeout(() => startSpeech(), 300);
        }
      } else if(speakModal ==="OpenAi"){
        stopAudio();

      }
      else{
        stopStreamedAudio();
        setSpeakingMessageId(null);
        if (shouldAutoRestartMic()) {
          startSpeech();
        }
      }
      setSpeakingMessageId(null);
      return;
    }
    
    // If speaking another message, stop it first
    if (speakingMessageId) {
      //console.log('ChatModal: Stopping previous speech');
      if(speakModal === "webkit"){
        stopSpeaking();
      } else if(speakModal ==="OpenAi"){
        stopAudio();
      }
      else{
        stopStreamedAudio();
        setSpeakingMessageId(null);
        if (shouldAutoRestartMic()) {
          startSpeech();
        }
      }
    }

    // Start speaking the new message
    //console.log('ChatModal: Starting to speak new message');
    setSpeakingMessageId(messageId);

    // Set the current message ID in the service
    setCurrentMessageId(messageId);
    if(speakModal === "webkit"){
      speak(text, {
        rate: 1.0, // Normal speed
        pitch: 1.0, // Normal pitch
        volume: 1.0, // Full volume
        onStart: () => {
          //console.log('ChatModal: Speech started for message:', messageId);
        },
        onEnd: () => {
          //console.log('ChatModal: Speech ended for message:', messageId);
          setSpeakingMessageId(null);
          // Only start speech recognition if not already listening and not processing
          if (shouldAutoRestartMic()) {
            startSpeech();
          }
        },
        onError: (error) => {
          console.error('ChatModal: Speech synthesis error:', error);
          setSpeakingMessageId(null);
          // Only start speech recognition if not already listening and not processing
          if (shouldAutoRestartMic()) {
           // startSpeech(); // Commented out - don't auto-restart on error
          }
        }
      });
     
    }else if(speakModal ==="OpenAi"){
      
    
    const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
    audioRef.current = audio;
    audio.onplay = () => {
      // setSpeakingMessageId('audio-response');
      // setLastResponseId('audio-response');
    };
    audio.onpause = () => {
      stopAudio();
    };
    // Reset speaking state when audio finishes
    audio.onended = () => {
      setSpeakingMessageId(null);
      
      //startSpeech();
      
    };
    audio.play();
  }
  else{
    playTTSChunks(messageId,text);
  }
  };

  // Fetch TTS audio for OpenAI TTS
  const fetchTTSAudio = async (messageId: string, text: string) => {
    console.log('🎵 fetchTTSAudio called for OpenAI TTS:', text);

    try {
      setSpeakingMessageId(messageId);

      // This would need to be implemented based on your OpenAI TTS service
      // For now, using a placeholder
      console.log('🎵 OpenAI TTS not implemented yet, falling back to webkit');

      // Fall back to webkit TTS for now
      handleSpeakMessage(messageId, text);
    } catch (error) {
      console.error('🎵 Error in OpenAI TTS:', error);
      setSpeakingMessageId(null);
      setIsProcessing(false);
    }
  };


  // Handle sending message via WorkAgent SignalR
  const handleSendMessage = async (text?: string) => {
    const messageToSend = text?.trim() ?? inputValue;

    // Check if this is a response to a user input request BEFORE adding user message
    const lastBotMessage = messages.length > 0 ? messages[messages.length - 1] : null;
    const isRespondingToInputRequest = lastBotMessage &&
                                      !lastBotMessage.isUser &&
                                      (lastBotMessage.userInputRequest || lastBotMessage.isInputRequest);


    // Add user message
    const userMessageId = generateMessageId();
    const userMessage: any = {
      text: messageToSend,
      isUser: true,
      timestamp: new Date(),
      id: userMessageId,
      guide: null
    };

    setMessages((prev: any) => [...prev!, userMessage]);

    setInputValue('');

    // Set processing state to true
    setIsProcessing(true);

    // Clear any existing message-specific callbacks before adding new ones to prevent accumulation
    // The persistent user input callback is managed separately and won't be affected
    console.log('🧹 Clearing existing message-specific callbacks before new message');
    const existingCallbacksCount = activeCallbacksRef.current.size;
    if (existingCallbacksCount > 0) {
      console.log(`⚠️ Found ${existingCallbacksCount} existing message-specific callbacks - cleaning them up to prevent duplicate responses`);

      // Remove all message-specific callbacks from the SignalR service
      activeCallbacksRef.current.forEach(callback => {
        console.log('🧹 Removing message-specific callback');
        workAgentSignalRService.removeCallbacks(callback);
      });

      // Clear the local reference
      activeCallbacksRef.current.clear();
      console.log('✅ All message-specific callbacks cleared. Persistent user input callback remains active.');
    }

    // Handle user input responses
    if (isRespondingToInputRequest) {
      // Get the userInputRequest object - it might be directly on the message or we need to create a default one
      const userInputRequest = lastBotMessage.userInputRequest || {
        FieldName: "user_input",
        Message: lastBotMessage.text,
        IsRequired: true,
        FieldType: "text"
      };

      // Send field value response directly through EXISTING SignalR connection (no auto-speak for user input)
      // User input should never be spoken - only bot responses should be spoken
      console.log('📝 Sending user input response without TTS:', messageToSend);

      try {
        await workAgentSignalRService.submitFieldValue(
          userInputRequest.FieldName,
          messageToSend
        );
        console.log(`✅ FIELD RESPONSE: Successfully submitted field value: ${userInputRequest.FieldName} = ${messageToSend}`);
        console.log('✅ FIELD RESPONSE: Callbacks remain active, waiting for backend response...');
        console.log('✅ FIELD RESPONSE: Microphone will restart after TTS completion (identical to normal prompts)');

        // Set processing to false - let the existing callbacks handle TTS and microphone restart
        // This ensures identical behavior to normal prompts
        setIsProcessing(false);

        return; // Don't send as regular prompt - callbacks remain active for next field
      } catch (error) {
        console.error('❌ FIELD RESPONSE: Error submitting field value:', error);
        // Fall back to regular prompt if field submission fails
      }
    } else {

    }

    // Create callbacks for this specific message
    const messageCallbacks: WorkAgentCallbacks = {
      onMessage: (response: any) => {
        console.log('WorkAgent message received:', response);
        console.log('🎵 Auto-speak enabled:', autoSpeakResponse, 'speakModal:', speakModal);
        
        // Determine the message text for skipDisplay check
        const messageText =  response.Message.trim();
const skipDisplay =
  messageText === "Form submitted successfully!" ||
  messageText.startsWith("Processing") ||
  messageText.includes("[[FeedbackInfo]]") ||
  messageText.includes("[[DONE]]");
        
        
        
        const botMessageId = generateMessageId();
        let botMessage: Message;

       
        // Handle different response types
         if (response.ResponseType === "guide") {
          if (response.Message == null || response.Message === "") {
            response.Message = "Here's the Guide, Click on Button to view the guide";
          }

          botMessage = {
            text: response.Message,
            isUser: false,
            timestamp: new Date(),
            id: botMessageId,
            guide: response.Guide,
            isStatusMessage: false
          };
          
          // Add message to chat unless it should be skipped
          if (!skipDisplay) {
            setMessages((prev: any) => [...prev, botMessage]);
          }
        }
        
       else if (response.Message.startsWith('[[FeedbackInfo]]')) {
          const match = response.Message.match(/\[\[FeedbackInfo\]\]\s*(true|false)/i);
          if (match) {
            const isTrue = match[1].toLowerCase() === 'true';
            // setShowFeedbackPopup(isTrue); // Removed: do not show popup immediately
            setFeedbackInfoState(isTrue);
          }
        }
        else if (speakModal === "webkit" ||
                (speakModal !== "webkit" && !response.Message.startsWith("[AUDIO]"))) {
          // Default to "message" type or any other type

          // Check if this is a user input request
          const isInputRequest = response.Message.toLowerCase().includes('please provide') ||
                                response.Message.toLowerCase().includes('enter') ||
                                response.Message.toLowerCase().includes('required') ||
                                response.Message.toLowerCase().includes('input') ||
                                response.Message.toLowerCase().includes('specify') ||
                                response.Message.toLowerCase().includes('choose');

          botMessage = {
            text: response.Message,
            isUser: false,
            timestamp: new Date(),
            id: botMessageId,
            guide: null,
            isStatusMessage:
              response.Message.startsWith("🔍") ||
              response.Message.startsWith("💬") ||
              response.Message.startsWith("🤖") ||
              response.Message.startsWith("📥") ||
              response.Message.startsWith("🧠") ||
              response.Message.startsWith("Processing"),
            isInputRequest: false
          };

           if (!skipDisplay) {
    setMessages((prev: any) => [...prev, botMessage]);
  }
  if (response.Message.startsWith('[[DONE]]')) {
          workAgentSignalRService.removeCallbacks(messageCallbacks);
          return;
        }

        }

        // Handle audio responses for non-webkit TTS
        if (speakModal !== "webkit" && response.Message.startsWith("[AUDIO]")) {
          base64Audio = response.Message.replace("[AUDIO]", "");
          const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
          audioRef.current = audio;

          // Set speaking state when audio starts playing
          audio.onplay = () => {
            console.log('Audio started playing');
          };

          audio.onpause = () => {
            stopAudio();
          };

          // Reset speaking state when audio finishes
          audio.onended = () => {
            setSpeakingMessageId(null);
            console.log('🎤 Audio response ended, restarting microphone if needed');
            if (shouldAutoRestartMic()) {
              setTimeout(() => {
                startSpeech();
              }, 500);
            }
          };

          // Handle errors
          audio.onerror = () => {
            console.error("Error playing audio response");
            setSpeakingMessageId(null);
          };

          audio.play();
        }
 // FeedbackInfo detection
        if (typeof response.Message === 'string' && response.Message.includes('[[FeedbackInfo]]')) {
          const match = response.Message.match(/\[\[FeedbackInfo\]\]\s*(true|false)/i);
          if (match) {
            const isTrue = match[1].toLowerCase() === 'true';
            // setShowFeedbackPopup(isTrue); // Removed: do not show popup immediately
            setFeedbackInfoState(isTrue);
          }
        }else
        // Auto-speak functionality for completed responses
        if (!response.Message.startsWith("Processing")) {
          console.log('🎵 Message completed, checking auto-speak conditions...');
          console.log('🎵 autoSpeakResponse:', autoSpeakResponse);
          console.log('🎵 Message starts with [AUDIO]:', response.Message.startsWith("[AUDIO]"));
          console.log('🎵 Message text:', response.Message.substring(0, 100) + '...');
          console.log('🎵 skipDisplay (Form submitted successfully):', skipDisplay);

          const messageId = botMessageId;
          const messageText = response.Message;

          setLastResponseId(messageId);

          // Auto-speak if enabled, not an audio response, and not a skipped message
          if (autoSpeakResponse && !response.Message.startsWith("[AUDIO]") && !skipDisplay) {
            console.log('🎵 ✅ Auto-speaking response with speakModal:', speakModal);
            setSpeakingMessageId(messageId);
            // Wait a short moment for the UI to update
            setTimeout(() => {
              if (speakModal === "webkit") {
                console.log('🎵 Using webkit TTS for auto-speak');
                handleSpeakMessage(messageId, messageText);
              } else if (speakModal === "OpenAi") {
                console.log('🎵 Using OpenAI TTS for auto-speak');
                // For OpenAI, we need to fetch the audio first, then play it
                fetchTTSAudio(messageId, messageText);
              } else {
                console.log('🎵 Using ElevenLabs streaming TTS for auto-speak');
                playTTSChunks(messageId, messageText);
              }
            }, 300);
          } else {
            console.log('🎵 ❌ Auto-speak conditions not met');
            console.log('🎵 autoSpeakResponse:', autoSpeakResponse);
            console.log('🎵 isAudioResponse:', response.Message.startsWith("[AUDIO]"));
            console.log('🎵 skipDisplay (Form submitted successfully):', skipDisplay);
          }
        } else {
          console.log('🎵 Message is processing, skipping auto-speak');
        }

        // If this is not a processing message, mark as complete
        if (!response.Message.startsWith("Processing")) {
          setIsProcessing(false);
          
          // Check for specific completion messages
          const isFormSubmittedSuccessfully = response === "Form submitted successfully!" || response.Message === "Form submitted successfully!";
          const isOtherFinalCompletionMessage = response.Message.includes("Request cancelled") ||
                                               response.Message.includes("Workflow completed") ||
                                               response.Message.includes("Process finished");
          const isAcknowledgment = response.Message.startsWith("Thank you for providing");
          const isIntermediateMessage = response.Message.includes("Data binding completed") ||
                                       response.Message.includes("You can now proceed") ||
                                       response.Message.includes("DOM data") ||
                                       response.Message.includes("continuing with");

          const isInputRequest = response.Message.toLowerCase().includes('please provide') ||
                                response.Message.toLowerCase().includes('enter') ||
                                response.Message.toLowerCase().includes('required') ||
                                response.Message.toLowerCase().includes('input') ||
                                response.Message.toLowerCase().includes('specify') ||
                                response.Message.toLowerCase().includes('choose');

          // Handle "Form submitted successfully!" as a special case for request completion
          if (isFormSubmittedSuccessfully) {
            console.log('✅ FORM SUBMITTED SUCCESSFULLY - Request completed, cleaning up callbacks and preparing for new request');
            workAgentSignalRService.removeCallbacks(messageCallbacks);
            activeCallbacksRef.current.delete(messageCallbacks);
            console.log(`📊 Active callbacks after form submission completion: ${activeCallbacksRef.current.size}`);
            console.log(`📊 SignalR service active callbacks after form submission completion: ${workAgentSignalRService.getActiveCallbacksCount()}`);
            
            // Reset processing state to allow new requests
            setIsProcessing(false);
            
            // Restart microphone for new input if conditions are met
            if (shouldAutoRestartMic()) {
              console.log('🎤 Restarting microphone for new request after form submission');
              setTimeout(() => {
                startSpeech();
              }, 1000); // Give a bit more time for TTS to complete if auto-speak is enabled
            }
          }
          // Handle other final completion messages
          else if (!isInputRequest && !isAcknowledgment && !isIntermediateMessage && isOtherFinalCompletionMessage) {
            console.log('🧹 Removing callbacks due to other FINAL completion message:', response.Message);
            workAgentSignalRService.removeCallbacks(messageCallbacks);
            activeCallbacksRef.current.delete(messageCallbacks);
            console.log(`📊 Active callbacks after removal: ${activeCallbacksRef.current.size}`);
            console.log(`📊 SignalR service active callbacks after removal: ${workAgentSignalRService.getActiveCallbacksCount()}`);
          } else if (isIntermediateMessage) {
            console.log('🔄 Keeping callbacks active for intermediate message:', response.Message);
          }
        }
      },

      onError: (error: string) => {
        console.error('ChatModal: Error from WorkAgent SignalR service:', error);

        // const errorMsg: Message = {
        //   text: `Error: ${error}`,
        //   isUser: false,
        //   timestamp: new Date(),
        //   id: generateMessageId(),
        //   guide: null,
        //   isStatusMessage: true
        // };

        // setMessages((prev: any) => [...prev, errorMsg]);
        setIsProcessing(false);

        // Remove callbacks on error
        console.log('🧹 Removing callbacks due to error:', error);
        workAgentSignalRService.removeCallbacks(messageCallbacks);
        activeCallbacksRef.current.delete(messageCallbacks);
        console.log(`📊 Active callbacks after error cleanup: ${activeCallbacksRef.current.size}`);
        console.log(`📊 SignalR service active callbacks after error cleanup: ${workAgentSignalRService.getActiveCallbacksCount()}`);

        if (shouldAutoRestartMic()) {
          startSpeech();
        }
      },

      // onUserInputRequest is now handled by the persistent callback, not message-specific callbacks

      onRequestConfirmation: (confirmationRequest: { Message: string; Data: Record<string, string> }) => {
        console.log('WorkAgent Confirmation Request:', confirmationRequest);

        const botMessageId = generateMessageId();

        // Create bot message for the confirmation request
        const botMessage: Message = {
          text: confirmationRequest.Message,
          isUser: false,
          timestamp: new Date(),
          id: botMessageId,
          guide: null,
          isStatusMessage: false,
          isInputRequest: true,
          userInputRequest: {
            Message: confirmationRequest.Message,
            FieldName: "confirmation",
            IsRequired: true,
            FieldType: "confirmation"
          }
        };

        setMessages((prev: any) => [...prev, botMessage]);
        setIsProcessing(false); // Allow user to respond

        // Set the last response ID for confirmation requests
        setLastResponseId(botMessageId);

        // Auto-speak the confirmation request message
        if (autoSpeakResponse) {
          console.log('🎵 Auto-speaking confirmation request message:', confirmationRequest.Message);
          setSpeakingMessageId(botMessageId);

          // Wait a short moment for the UI to update, then speak the request
          setTimeout(() => {
            if (speakModal === "webkit") {
              console.log('🎵 Using webkit TTS for confirmation request auto-speak');
              handleSpeakMessage(botMessageId, confirmationRequest.Message);
            } else if (speakModal === "OpenAi") {
              console.log('🎵 Using OpenAI TTS for confirmation request auto-speak');
              fetchTTSAudio(botMessageId, confirmationRequest.Message);
            } else {
              console.log('🎵 Using ElevenLabs streaming TTS for confirmation request auto-speak');
              playTTSChunks(botMessageId, confirmationRequest.Message);
            }
          }, 300);
        } else {
          // If auto-speak is disabled, start microphone immediately
          if (shouldAutoRestartMic()) {
            console.log('🎤 Starting microphone for confirmation request (auto-speak disabled)');
            setTimeout(() => {
              startSpeech();
            }, 500); // Small delay to ensure UI updates
          }
        }

        // Keep callbacks active for confirmation response
      },

      onNavigationRequest: async (navigationRequest: { Url: string; Id: string; Message: string }) => {
        const botMessageId = generateMessageId();

        setMessages((prev: any) => [
          ...prev,
          {
            text: `${navigationRequest.Message}\n\nRedirecting to: ${navigationRequest.Url}`,
            isUser: false,
            timestamp: new Date(),
            id: botMessageId,
            guide: null,
            isStatusMessage: true
          }
        ]);

        if (setAgentId) {
          setAgentId(navigationRequest.Id);
        }

        const currentUrl = window.location.href.replace(/\/$/, '');
        const targetUrl = navigationRequest.Url.replace(/\/$/, '');

        if (currentUrl === targetUrl) {
          await workAgentSignalRService.confirmNavigation(true, 'Already on the target page');
          setMessages((prev: any) => [
            ...prev,
            {
              text: '✅ Already on the target page!',
              isUser: false,
              timestamp: new Date(),
              id: generateMessageId(),
              guide: null,
              isStatusMessage: true
            }
          ]);
          return;
        }

        // If not on the correct page, get agent and filter click steps
        try {
          const agent = await GetAgentById(navigationRequest.Id);

          if (!agent || !agent.TrainingFields) {
            console.error('Agent not found or has no TrainingFields');
            setMessages((prev: any) => [
              ...prev,
              {
                text: '❌ Agent not found or has no training fields',
                isUser: false,
                timestamp: new Date(),
                id: generateMessageId(),
                guide: null,
                isStatusMessage: true
              }
            ]);
            return;
          }

           const clickObject = agent.TrainingFields.filter((field: any) => {
  const isClick = field.Type === "click" || field.Type === "hover";
  const hasButtonInPath =
    (field.Xpath && !field.Xpath.toLowerCase().includes("button"))

  return isClick && hasButtonInPath;
});
const mappedData = clickObject.map((item: any) => ({
    name: item.Name,
    xpath: item.Xpath,
    selector: item.Selector,
    labelName: item.LabelName,
    cssSelector: item.CSSSelector,
    type: item.Type,
    value: item.Value,
  }));
          // Store clickObject if needed (e.g., in state or context)
          // Here, we use setBindingData if available to pass clickObject to the work agent
          if (setBindingData) {
            setBindingData(mappedData);
          }
          // Optionally, you can also trigger the work agent UI or background mode
          if (setBackgroundMode) setBackgroundMode(true);
          if (setWorkerAgentVisible) setWorkerAgentVisible(false);
          if (setStartTraining) setStartTraining(true);

          // After work agent completes, re-check the URL and proceed if matched
          // We'll use a polling approach for simplicity
          const checkUrlAndProceed = async () => {
            const pollInterval = 1000; // 1 second
            const maxAttempts = 30; // 30 seconds max
            let attempts = 0;
            const intervalId = setInterval(async () => {
              const currentUrlNow = window.location.href.replace(/\/$/, '');
              if (currentUrlNow === targetUrl) {
                clearInterval(intervalId);
                // Confirm navigation and proceed with next steps
                await workAgentSignalRService.confirmNavigation(true, 'Navigation completed after click actions');
                setMessages((prev: any) => [
                  ...prev,
                  {
                    text: '✅ Redirected and navigation confirmed after click actions!',
                    isUser: false,
                    timestamp: new Date(),
                    id: generateMessageId(),
                    guide: null,
                    isStatusMessage: true
                  }
                ]);
                // Optionally, trigger next steps (PromptDataExtraction, DomDataBinding, etc.)
                // This depends on your agent flow, but you may need to call setStartTraining or similar again
              }
              attempts++;
              if (attempts >= maxAttempts) {
                clearInterval(intervalId);
                setMessages((prev: any) => [
                  ...prev,
                  {
                    text: '⚠️ Navigation did not complete in time after click actions.',
                    isUser: false,
                    timestamp: new Date(),
                    id: generateMessageId(),
                    guide: null,
                    isStatusMessage: true
                  }
                ]);
              }
            }, pollInterval);
          };
          checkUrlAndProceed();
        } catch (error) {
          console.log(error, 'error');
          setMessages((prev: any) => [
            ...prev,
            {
              text: `❌ Error fetching agent or processing click actions: ${error}`,
              isUser: false,
              timestamp: new Date(),
              id: generateMessageId(),
              guide: null,
              isStatusMessage: true
            }
          ]);
        }
      },

      onNavigationConfirmation: (navigationconfirmation: { success: boolean, message: string }) => {
        console.log('WorkAgent Navigation Confirmation:', navigationconfirmation);

        const botMessageId = generateMessageId();
        const botMessage: Message = {
          text: navigationconfirmation.success ?
            `✅ ${navigationconfirmation.message}` :
            `❌ ${navigationconfirmation.message}`,
          isUser: false,
          timestamp: new Date(),
          id: botMessageId,
          guide: null,
          isStatusMessage: true
        };

        setMessages((prev: any) => [...prev, botMessage]);
      }
    };

    // Track this callback for cleanup
    activeCallbacksRef.current.add(messageCallbacks);
    console.log(`📊 Active callbacks after adding new one: ${activeCallbacksRef.current.size}`);
    console.log(`📊 SignalR service active callbacks: ${workAgentSignalRService.getActiveCallbacksCount()}`);
    
    // Safety check: Ensure we only have one active callback at a time
    if (activeCallbacksRef.current.size > 1) {
      console.warn(`⚠️ WARNING: Multiple callbacks detected (${activeCallbacksRef.current.size}). This should not happen after the fix.`);
    }

    try {
      console.log('🆕 NEW MESSAGE: Creating new SignalR request with processPrompt()');
      await workAgentSignalRService.processPrompt(messageToSend, messageCallbacks);
    } catch (error) {
      console.error('Error sending prompt to WorkAgent SignalR:', error);

      // Clean up callbacks on error
      console.log('🧹 Removing callbacks due to processPrompt error:', error);
      workAgentSignalRService.removeCallbacks(messageCallbacks);
      activeCallbacksRef.current.delete(messageCallbacks);
      console.log(`📊 Active callbacks after processPrompt error cleanup: ${activeCallbacksRef.current.size}`);
      console.log(`📊 SignalR service active callbacks after processPrompt error cleanup: ${workAgentSignalRService.getActiveCallbacksCount()}`);

      // Add error message to chat
      const errorMsg: Message = {
        text: `Error: Failed to connect to the WorkAgent. ${error instanceof Error ? error.message : 'Please try again.'}`,
        isUser: false,
        timestamp: new Date(),
        id: generateMessageId(),
        guide: null,
        isStatusMessage: true
      };

      setMessages((prev: any) => [...prev, errorMsg]);
      setIsProcessing(false);
    }
  };
  
  const stopAudio = (startListening: boolean = true) => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0; // Reset to beginning
      audioRef.current = null;
      setSpeakingMessageId(null);
      if (shouldAutoRestartMic() && startListening) {
        startSpeech();
      }
    }
  };
  
  

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isProcessing && !isListening && inputValue.trim() !== '') {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Helper function to check if microphone should auto-restart
  const shouldAutoRestartMic = (): boolean => {
    return !micManuallyDisabled && !isListening && !isProcessing && open;
  };

  // Toggle WebKit speech recognition
  const startSpeech = async (isManualStart: boolean = false) => {
    // Clear any previous error
    setMicPermissionError(null);

    // Don't start speech recognition if already processing a message
    if (isProcessing) {
      console.log('ChatModal: Cannot start speech recognition while processing a message');
      return;
    }

    // If this is not a manual start and mic was manually disabled, don't auto-start
    if (!isManualStart && micManuallyDisabled) {
      console.log('ChatModal: Microphone manually disabled, skipping auto-start');
      return;
    }

    if (isListening) {
      //console.log('ChatModal: Stopping WebKit speech recognition');
      stopSpeechRecognition();
      setIsListening(false);
      // When user manually stops the mic, mark it as manually disabled
      if (isManualStart) {
        setMicManuallyDisabled(true);
        console.log('ChatModal: Microphone manually disabled by user');
      }
      // Input value is already set with the latest transcript
    } else {
      //console.log('ChatModal: Starting WebKit speech recognition');

      // First, explicitly request microphone permission
      const permissionGranted = await requestMicrophonePermission();

      if (!permissionGranted) {
        //console.log('ChatModal: Microphone permission denied for WebKit');
        setMicPermissionStatus('denied');
        setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
        return;
      }

      // Update permission status
      setMicPermissionStatus('granted');
      setMicPermissionError(null);

      setIsListening(true);
      // When user manually starts the mic, reset the manual disable flag
      if (isManualStart) {
        setMicManuallyDisabled(false);
        console.log('ChatModal: Microphone manually enabled by user');
      }
      // Clear the input field when starting speech recognition
      setInputValue('');

      try {
        startSpeechRecognition({
          onStart: () => {
             setIsListening(true);
            setMicPermissionError(null);
          },
          onResult: (text, _isFinal) => {
            setInputValue(text);
          },
          onEnd: (text: string) => {
            setIsListening(false);
            // Only send the message if we're not already processing another message
            if (!isProcessing && text.trim() !== '') {
              handleSendMessage(text);
            } else if (isProcessing) {
              console.log('ChatModal: Cannot send message while processing another message');
            }
          },
          onError: (error) => {
            console.error('ChatModal: WebKit speech recognition error:', error);
            setIsListening(false);
          }

          // let errorMessage = 'Speech recognition error';
          // if (typeof error === 'string') {
          //   errorMessage = error;
          // }

          // Check for permission errors
          // if (errorMessage.toLowerCase().includes('permission') ||
          //     errorMessage.toLowerCase().includes('denied') ||
          //     errorMessage.toLowerCase().includes('access')) {
          //   errorMessage = 'Microphone permission denied. Please allow microphone access in your browser settings.';
          //   setMicPermissionStatus('denied');
          // }

          // setMicPermissionError(errorMessage);

          // Add error message to chat
          //   const errorMsg: Message = {
          //     text: `Microphone error: ${errorMessage}`,
          //     isUser: false,
          //     timestamp: new Date(),
          //     id: generateMessageId(),
          //     guide: null
          //   };
          //   setMessages(prev => [...prev, errorMsg]);
          // },

          // pauseDuration: 2000 // Stop after 2 seconds of silence
        });
      } catch (error) {
        console.error('ChatModal: Failed to start WebKit speech recognition:', error);
        //setIsListening(false);

        // Set user-friendly error message
        let errorMessage = 'Failed to start speech recognition';
        if (error instanceof Error) {
          errorMessage = error.message;
        }

        // Check for permission errors
        if (errorMessage.toLowerCase().includes('permission') ||
          errorMessage.toLowerCase().includes('denied') ||
          errorMessage.toLowerCase().includes('access')) {
          errorMessage = 'Microphone permission denied. Please allow microphone access in your browser settings.';
          setMicPermissionStatus('denied');
        }

        setMicPermissionError(errorMessage);

        // Add error message to chat
        const errorMsg: Message = {
          text: `Microphone error: ${errorMessage}`,
          isUser: false,
          timestamp: new Date(),
          id: generateMessageId(),
          guide: null
        };
        setMessages((prev:any) => [...prev, errorMsg]);
      }
    }
  };

  // Function to toggle auto-speak for responses
  const toggleAutoSpeak = () => {
    setAutoSpeakResponse(prev => !prev);
  };


  const handleCloseModal = useCallback(() => {


    // Stop listening and reset states FIRST
    setIsListening(false);
    setReWelcomeMessageShown(false);
    setMicManuallyDisabled(false); // Reset manual disable state when modal closes
    stopSpeechRecognition();

    // Stop speaking based on the speaking modal type
    if (speakingModal === "OpenAi") {

      stopAudio(false);
    } else if (speakingModal === "ElevenLabs") {

      stopStreamedAudio();
    } else {

      stopStreamedAudio();
    }

    // Clean up speaking state
    if (speakingMessageId) {
     
      stopSpeaking();
      setSpeakingMessageId(null);
      setSpeechRecognitionSupported(false);
    }

    // Close the modal LAST
    onClose();
   
  }, [speakingModal, speakingMessageId, onClose, stopSpeechRecognition, stopAudio, stopStreamedAudio, stopSpeaking, setSpeakingMessageId, setSpeechRecognitionSupported, setIsListening, setReWelcomeMessageShown, setMicManuallyDisabled]);

console.log(messages,"messages");
  const getConnectionStatusIcon = () => {
    switch (connectionState) {
      case signalR.HubConnectionState.Connected:
        return <WifiIcon sx={{ color: '#4caf50', fontSize: '18px' }} />;
      case signalR.HubConnectionState.Connecting:
      case signalR.HubConnectionState.Reconnecting:
        return <CircularProgress size={16} sx={{ color: '#ff9800' }} />;
      case signalR.HubConnectionState.Disconnected:
      default:
        return <WifiOffIcon sx={{ color: '#f44336', fontSize: '18px' }} />;
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionState) {
      case signalR.HubConnectionState.Connected:
        return 'Connected';
      case signalR.HubConnectionState.Connecting:
        return 'Connecting...';
      case signalR.HubConnectionState.Reconnecting:
        return 'Reconnecting...';
      case signalR.HubConnectionState.Disconnected:
      default:
        return 'Disconnected';
    }
  };

  return (<>{
    open &&
    (
      <Paper sx={{
        position: "relative",
        width: "380px",
        display: "flex",
        flexDirection: "column",
        borderRadius: "0px",
        boxShadow: "0px 0px 15px rgba(0,0,0,0.2)",
        overflow: "hidden",
        zIndex: "1",
        height: "100vh",
        background: '#F8F9FA',
      }} elevation={3}>
        {/* Chat Header */}
        <Box sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "8px 12px",
          color: "white",
          position: "relative",
          overflow: "hidden",
          boxShadow: "0px 5px 10px rgba(0, 0, 0, 0.2)",
          "&::before": {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: '#4361EE',
            zIndex: -1
          }
        }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}>
            <img
                src={robot}
              alt="Chat"
              style={{
                width: "36px",
                height: "36px"
              }}
            />
            <Typography
              sx={{
                background: '#fff',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: "22px",
                fontWeight: "600",
                fontFamily: "Gotham Pro",
              }}>Dona</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Auto-speak toggle button */}
            <Tooltip
              title={autoSpeakResponse ? "Turn off auto-speak" : "Turn on auto-speak"}
              componentsProps={{
                tooltip: {
                  sx: {
                    fontSize: "12px",
                    fontFamily: "Gotham Pro",
                    color: "white",
                  }
                }
              }}
            >
              <IconButton
                onClick={toggleAutoSpeak}
                size="small"
                sx={{
                  color: 'white', marginRight: 1,
                  svg: {
                    fontSize: "18px"
                  }
                }}
              >
                {autoSpeakResponse ? <VolumeUpIcon /> : <VolumeOffIcon />}
              </IconButton>
            </Tooltip>

            {/* Connection status indicator */}
            <Tooltip
                           title={`WorkAgent ${getConnectionStatusText()}`}
                           componentsProps={{
                             tooltip: {
                               sx: {
                                 fontSize: "12px",
                                 fontFamily: "Gotham Pro",
                                 color: "white",
                               }
                             }
                           }}
                         >
                           <Box sx={{ marginRight: 1, display: 'flex', alignItems: 'center' }}>
                             {getConnectionStatusIcon()}
                           </Box>
                         </Tooltip>


              {/* <IconButton
                size="small"
                sx={{
                  color: 'white', marginRight: 1,
                  svg: {
                    fontSize: "18px"
                  }
                }}
              >
                {connectionState === signalR.HubConnectionState.Connected ? (
                  <WifiIcon />
                ) : (
                  <WifiOffIcon />
                )}
              </IconButton> */}  
               {/* Disconnect button - Always visible */}
                            <Tooltip
                              title={
                                connectionState === signalR.HubConnectionState.Connected
                                  ? "Disconnect from WorkAgent"
                                  : connectionState === signalR.HubConnectionState.Connecting
                                    ? "Connecting to WorkAgent..."
                                    : "Connect to WorkAgent"
                              }
                              componentsProps={{
                                tooltip: {
                                  sx: {
                                    fontSize: "12px",
                                    fontFamily: "Gotham Pro",
                                    color: "white",
                                  }
                                }
                              }}
                            >
               <IconButton
                                size="small"
                                onClick={async () => {
                                  try {
                                    if (connectionState === signalR.HubConnectionState.Connected) {
                                      // Disconnect if connected
                                      await workAgentSignalRService.disconnect();
                                      setConnectionState(signalR.HubConnectionState.Disconnected);
                                    } else if (connectionState === signalR.HubConnectionState.Disconnected) {
                                      // Connect if disconnected
                                      await workAgentSignalRService.ensureConnection();
                                      setConnectionState(workAgentSignalRService.getConnectionState());
                                    }
                                  } catch (error) {
                                    console.error('Error toggling connection:', error);
                                  }
                                }}
                                disabled={connectionState === signalR.HubConnectionState.Connecting || connectionState === signalR.HubConnectionState.Reconnecting}
                                sx={{
                                  color: 'white',
                                  marginRight: 1,
                                  svg: {
                                    fontSize: "16px"
                                  },
                                  '&.Mui-disabled': {
                                    color: 'rgba(255, 255, 255, 0.5)',
                                  }
                                }}
                              >
                                {connectionState === signalR.HubConnectionState.Connected ? (
                                  <WifiOffIcon />
                                ) : (
                                  <WifiIcon />
                                )}
                              </IconButton>
            </Tooltip>

            <IconButton
              size="small"
              onClick={handleCloseModal}
              sx={{
                color: 'white',
                svg: {
                  fontSize: "18px"
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

        </Box>

        {/* Chat Body - Messages */}
        <Box sx={{
          flexGrow: 1,
          overflowY: "auto",
          padding: "0px",
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          height: "calc(90vh - 110px)",
          placeContent: "flex-end",
        }}>
          <Box sx={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            width: "100%",
            height: "auto",
            overflow: "auto",
            padding: "16px",
          }}>
            {messages.map((message:any) => (
              <Box
                key={message.id || `msg-${message.timestamp.getTime()}`}
                sx={{
                  alignSelf: message.isUser ? "flex-end" : "flex-start",
                  width: "auto",
                  maxWidth: "100%",
                  wordBreak: "break-word",
                }}
              >

                <Box sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  width: '100%'
                }}>
                  {
                    message.isUser
                      ?
                      <Box sx={{
                        width: '100%',
                        textAlign: "right"
                      }}>
                        <Typography sx={{
                          fontSize: "14px",
                          color: " #000",
                          fontFamily: "Gotham Pro",
                          borderWidth: "0px",
                          padding: "12px 14px",
                          background: "#DEE2E6",
                          width: "fit-content",
                          borderRadius: "20px",
                          float: "right",
                          // maxWidth: "95%"
                        }}>
                          {message.text}
                        </Typography>
                      </Box>
                      :
                      <Box sx={{ flex: 1 }}>
                       {
                            message.guide == null && (
                              <Box sx={{
                                position: 'relative',
                                // Add special styling for input request messages
                                ...(message.isInputRequest && {
                                  border: '2px solid #ff9800',
                                  borderRadius: '8px',
                                  padding: '8px',
                                  backgroundColor: '#fff3e0',
                                  '&::before': {
                                    content: '"⚡"',
                                    position: 'absolute',
                                    top: '-8px',
                                    right: '8px',
                                    backgroundColor: '#ff9800',
                                    color: 'white',
                                    borderRadius: '50%',
                                    width: '20px',
                                    height: '20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '12px',
                                  }
                                })
                              }}>
                                <AIResponseDisplay text={message.text} />
                                {/* Show input prompt indicator for input request messages */}
                                {message.isInputRequest && (
                                  <Box sx={{ marginTop: '8px' }}>
                                    {/* Field information */}
                                    {message.userInputRequest && (
                                      <Box sx={{
                                        padding: '8px',
                                        backgroundColor: '#f5f5f5',
                                        borderRadius: '4px',
                                        marginBottom: '8px',
                                        border: '1px solid #ddd'
                                      }}>
                                        <Typography sx={{ fontSize: '12px', fontWeight: 'bold', color: '#666' }}>
                                          Field: {message.userInputRequest.FieldName}
                                        </Typography>
                                        <Typography sx={{ fontSize: '12px', color: '#666' }}>
                                          Type: {message.userInputRequest.FieldType}
                                        </Typography>
                                        {message.userInputRequest.IsRequired && (
                                          <Typography sx={{ fontSize: '12px', color: '#d32f2f', fontWeight: 'bold' }}>
                                            * Required
                                          </Typography>
                                        )}
                                      </Box>
                                    )}
                                    {/* Waiting indicator */}
                                    <Box sx={{
                                      padding: '4px 8px',
                                      backgroundColor: '#ff9800',
                                      color: 'white',
                                      borderRadius: '4px',
                                      fontSize: '12px',
                                      fontWeight: 'bold',
                                      display: 'inline-block'
                                    }}>
                                      💬 Waiting for your input...
                                    </Box>
                                  </Box>
                                )}
                              </Box>
                            )
                          }
                      
                        {

                          message.guide != null &&
                          <>
                            <Button variant="outlined" size="small" color="primary" onClick={async () => {
                              // Stop all audio/speech first
                              setIsOpen(false); setIsFromAi(true); setGuide(message.guide);
                              stopSpeechRecognition();
                              setReWelcomeMessageShown(false);
                              setIsListening(false);
                              if(speakModal ==="OpenAi"){
                                stopAudio(false);
                              }
                              else if(speakModal ==="ElevenLabs")
                              {
                             stopStreamedAudio();
                              }
                              if (speakingMessageId) {
                                stopSpeechRecognition();
                                stopSpeaking();
                                setSpeakingMessageId(null);
                              }
                            }}
                              sx={{
                                color: "#4361ee",
                                borderColor: "#4361ee",
                                marginTop: "8px",
                                fontSize: "10px",
                                fontWeight: "600",
                                "&:hover": {
                                  color: "#fff",
                                  borderColor: "#4361ee",
                                  background: "#4361ee",
                                },
                              }}
                            >{message.text}</Button>
                          </>

                        }
                      </Box>
                  }

                  {/* Text-to-speech button only for the last bot message */}
                  {!message.isUser && message.id && (
                    <Tooltip title={speakingMessageId === message.id ? "Stop speaking" : "Listen again"}
                      componentsProps={{
                        tooltip: {
                          sx: {
                            fontSize: "12px",
                            fontFamily: "Gotham Pro",
                            color: "white",
                          }
                        }
                      }}
                    >
                      <IconButton
                        disabled={isListening || message.id !== lastResponseId}
                        onClick={() => { if (isListening) return; handleSpeakMessage(message.id!, message.text) }}
                        size="small"
                        sx={{
                          marginRight: '10px',
                          color: '#000',
                          '&:hover': {
                            color: '#000',
                            background: "transparent",
                          },
                          // color: speakingMessageId === message.id ? "#5f9ea0" : "inherit"
                        }}
                      >
                        {speakingMessageId === message.id ? <VolumeUpIcon /> : <VolumeOffIcon />}
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Box>
        </Box>

        {/* Chat Footer - Input */}
        <Box sx={{
          background: '#e7e7e7',
          display: "flex",
          alignItems: "center",
          gap: "8px",
          padding: "0px 14px",
        }}>
          <Box sx={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            margin: "10px 0px",
            paddingRight: "10px",
            width: "100%",
            borderRadius: "10px",
            background: "transparent",
            border: "1px solid #a3a3a3",
          }}>
            {/* TextField */}
            <TextField
              fullWidth
              multiline
              placeholder={
                isListening
                  ? "🎤 Listening"
                  : isProcessing
                    ? "Processing your request"
                    : connectionState !== signalR.HubConnectionState.Connected
                      ? "Connecting to WorkAgent"
                      : isWaitingForInput()
                        ? "💬 Speak or type your response"
                        : micManuallyDisabled
                          ? "Type your message"
                          : "Type or use voice input"
              }
              variant="outlined"
              size="small"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
                disabled={isListening || isProcessing || connectionState !== signalR.HubConnectionState.Connected}
              inputRef={inputRef}
              InputProps={{
                sx: {
                  padding: "6px 0px 6px 12px",
                  color: '#fff !important',
                  border: "0px !important",
                  '& textarea': {
                    minHeight: '25px',
                    maxHeight: '100px',
                    overflowY: 'auto !important',
                    height: "50px",
                    // placeContent: "center",
                    fontSize: "13px",
                    color: "#000",
                  },
                  '& fieldset': {
                    border: '0px',
                  },
                },
              }}
            />
            {/* Mic Button */}
            {speechRecognitionSupported && (
              <Tooltip
                title={
                  isListening
                    ? "Stop listening"
                    : micPermissionStatus === 'denied'
                      ? "Microphone permission denied. Click to request access."
                      : micManuallyDisabled
                        ? "Microphone manually disabled. Click to enable voice input."
                        : "Start voice input"
                }
                componentsProps={{
                  tooltip: {
                    sx: {
                      fontSize: "12px",
                      fontFamily: "Gotham Pro",
                    }
                  }
                }}
              >
                <IconButton
                    disabled={!!speakingMessageId || connectionState !== signalR.HubConnectionState.Connected}
                  onClick={
                    micPermissionStatus === 'denied'
                      ? handleRequestMicrophonePermission
                      : () => startSpeech(true) // Pass true for manual start
                  }
                  size="small"
                  sx={{
                    backgroundColor: 'transparent', // Purple background
                    color: '#000',
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                  }}
                >
                  {isListening ? (
                    <>
                      <MicIcon sx={{ fontSize: "20px" }} />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <CircularProgress
                          size={24}
                          thickness={3}
                          sx={{ color: '#000' }}
                        />
                      </Box>
                    </>
                  ) : micPermissionStatus === 'denied' ? (
                    <MicOffIcon sx={{ fontSize: "20px", color: '#f44336' }} />
                  ) : micManuallyDisabled ? (
                    <MicOffIcon sx={{ fontSize: "20px", color: '#000000' }} />
                  ) : (
                    <MicIcon sx={{ fontSize: "20px" }} />
                  )}
                </IconButton>
              </Tooltip>
            )}
          </Box>
          {/* Send Button */}
          <IconButton
              onClick={() => {
                handleSendMessage(); 
                if(speakModal !=="webkit"){
                stopAudio();
                }
              }}
              disabled={inputValue.trim() === '' || isListening || isProcessing || connectionState !== signalR.HubConnectionState.Connected}
            size="small"
            sx={{
              padding: "9px 8px 11px 12px",
              color: '#fff',
              backgroundColor: '#4361ee',
              '&.Mui-disabled': {
                backgroundColor: '#4361ee',
                opacity: 0.6,  // optional: add some visual cue it's disabled
                color: '#fff',
              },
              '&:hover': {
                backgroundColor: '#4361ee',
              },
            }}
          >
            <SendIcon sx={{
              fontSize: "20px",
              transform: "rotate(-35deg)",
            }} />
          </IconButton>
        </Box>

      </Paper>
    )
  }
  </>);
};

export default ChatModal;

